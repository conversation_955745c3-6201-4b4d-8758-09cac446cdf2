@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Scheherazade+New:wght@400;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure Islamic theme colors are available */
@layer utilities {
  .text-primary-600 { color: #43a047; }
  .text-primary-700 { color: #388e3c; }
  .text-primary-800 { color: #2e7d32; }
  .text-primary-300 { color: #81c784; }
  .text-primary-400 { color: #66bb6a; }

  .bg-primary-600 { background-color: #43a047; }
  .bg-primary-700 { background-color: #388e3c; }
  .bg-primary-100 { background-color: #c8e6c8; }
  .bg-primary-900 { background-color: #1b5e20; }

  .text-accent-600 { color: #c8a951; }
  .text-accent-400 { color: #f4d757; }

  .bg-secondary-50 { background-color: #fefcf8; }
  .bg-secondary-100 { background-color: #fdf8f0; }

  .border-primary-200 { border-color: #a5d6a5; }
  .border-primary-700 { border-color: #388e3c; }
  .border-secondary-300 { border-color: #f8e9d2; }

  .bg-dark-800 { background-color: #1a202c; }
  .bg-dark-950 { background-color: #0d1117; }
  .text-dark-200 { color: #e2e8f0; }
}

/* Islamic Theme Base Styles */
:root {
  --islamic-primary: #2e7d32;
  --islamic-secondary: #f5e9da;
  --islamic-accent: #c8a951;
  --islamic-text: #333333;
  --islamic-bg: #ffffff;
  --islamic-card: #ffffff;
  --islamic-border: #e8f5e8;
  --islamic-shadow: rgba(46, 125, 50, 0.1);
}

.dark {
  --islamic-primary: #4caf50;
  --islamic-secondary: #1a2c23;
  --islamic-accent: #c8a951;
  --islamic-text: #e0e0e0;
  --islamic-bg: #0d1117;
  --islamic-card: #1a2c23;
  --islamic-border: #2e7d32;
  --islamic-shadow: rgba(0, 0, 0, 0.3);
}

/* Custom component styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  font-family: 'Poppins', sans-serif;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0.75rem 1.5rem;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:focus-visible {
  outline: 2px solid var(--islamic-accent);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-primary {
  background: linear-gradient(135deg, #2e7d32, #388e3c);
  color: white;
  box-shadow: 0 4px 15px rgba(46, 125, 50, 0.2);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1b5e20, #2e7d32);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(46, 125, 50, 0.3);
}

.btn-secondary {
  background-color: var(--islamic-secondary);
  color: var(--islamic-text);
  border: 1px solid var(--islamic-border);
}

.btn-secondary:hover {
  background-color: #f2dfc3;
  transform: translateY(-1px);
}

.btn-outline {
  border: 2px solid var(--islamic-primary);
  background-color: transparent;
  color: var(--islamic-primary);
}

.btn-outline:hover {
  background-color: var(--islamic-primary);
  color: white;
  transform: translateY(-1px);
}

.btn-accent {
  background: linear-gradient(135deg, #c8a951, #d0bd7e);
  color: #333;
  box-shadow: 0 4px 15px rgba(200, 169, 81, 0.2);
}

.btn-accent:hover {
  background: linear-gradient(135deg, #b59849, #c8a951);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(200, 169, 81, 0.3);
}

/* Islamic Card Styles */
.islamic-card {
  background: white;
  border: 1px solid #e8f5e8;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(46, 125, 50, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dark .islamic-card {
  background: #1a2c23;
  border-color: #2e7d32;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.islamic-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #2e7d32, #c8a951);
}

.islamic-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(46, 125, 50, 0.15);
}

.dark .islamic-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
}

/* Prayer Time Widget */
.prayer-widget {
  background: linear-gradient(135deg, var(--islamic-card), var(--islamic-secondary));
  border-radius: 1rem;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.prayer-widget::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(200, 169, 81, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.prayer-active {
  background: linear-gradient(135deg, var(--islamic-primary), var(--islamic-accent));
  color: white;
  animation: prayer-glow 2s ease-in-out infinite alternate;
}

/* Tasbih Counter */
.tasbih-counter {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--islamic-primary), var(--islamic-accent));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  box-shadow: 0 8px 30px rgba(46, 125, 50, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tasbih-counter::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.tasbih-counter:active::before {
  width: 100%;
  height: 100%;
}

.tasbih-counter:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(46, 125, 50, 0.4);
}

/* Arabic Text Styling */
.arabic-text {
  font-family: 'Amiri', 'Scheherazade New', serif;
  font-size: 0.875rem;
  line-height: 1.6;
  text-align: right;
  direction: rtl;
  color: var(--islamic-text);
}

.quran-verse {
  font-family: 'Amiri', serif;
  font-size: 1rem;
  line-height: 1.8;
  text-align: center;
  color: var(--islamic-primary);
  padding: 1rem;
  background: linear-gradient(135deg, rgba(232, 245, 232, 0.3), rgba(245, 233, 218, 0.3));
  border-radius: 0.75rem;
  border: 1px solid var(--islamic-border);
  position: relative;
}

.quran-verse::before,
.quran-verse::after {
  content: '﴿';
  font-size: 1.25rem;
  color: var(--islamic-accent);
  position: absolute;
}

.quran-verse::before {
  top: 0.25rem;
  right: 0.75rem;
}

.quran-verse::after {
  content: '﴾';
  bottom: 0.25rem;
  left: 0.75rem;
}

/* Islamic Pattern Background */
.islamic-pattern {
  background-image: var(--islamic-pattern);
  position: relative;
}

.islamic-pattern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(232, 245, 232, 0.8), rgba(245, 233, 218, 0.8));
  pointer-events: none;
}

/* Additional Islamic Theme Utilities */
.prayer-active {
  background: linear-gradient(135deg, #2e7d32, #c8a951) !important;
  color: white !important;
  box-shadow: 0 2px 10px rgba(200, 169, 81, 0.3);
}

.btn-accent {
  background: linear-gradient(135deg, #c8a951, #d0bd7e);
  color: #333;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-accent:hover {
  background: linear-gradient(135deg, #b59849, #c8a951);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(200, 169, 81, 0.3);
}

.font-arabic {
  font-family: 'Amiri', 'Scheherazade New', serif;
}

.font-heading {
  font-family: 'Poppins', system-ui, sans-serif;
}

/* Responsive Islamic Typography */
@media (max-width: 768px) {
  .arabic-text {
    font-size: 0.75rem;
  }

  .quran-verse {
    font-size: 0.875rem;
    padding: 1rem;
  }

  .tasbih-counter {
    width: 60px;
    height: 60px;
    font-size: 1rem;
  }
}
