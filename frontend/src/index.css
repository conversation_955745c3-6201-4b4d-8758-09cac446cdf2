@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom component styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  padding: 0.5rem 1rem;
  border: none;
  cursor: pointer;
}

.btn:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #1f2937;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
}

.btn-outline {
  border: 1px solid #d1d5db;
  background-color: white;
  color: #374151;
}

.btn-outline:hover {
  background-color: #f9fafb;
}
