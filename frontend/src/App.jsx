import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { motion } from 'framer-motion';
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard';
import Notes from './pages/Notes';
import Tasks from './pages/Tasks';
import Habits from './pages/Habits';
import Health from './pages/Health';
import Knowledge from './pages/Knowledge';
import Finance from './pages/Finance';
import Islamic from './pages/Islamic';
import Settings from './pages/Settings';
import Login from './pages/Auth/Login';
import Register from './pages/Auth/Register';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import ProtectedRoute from './components/Auth/ProtectedRoute';

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-secondary-50 dark:bg-dark-950 islamic-pattern transition-colors duration-300">
            <Routes>
              {/* Public routes */}
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />

              {/* Protected routes */}
              <Route path="/" element={
                <ProtectedRoute>
                  <Layout>
                    <Dashboard />
                  </Layout>
                </ProtectedRoute>
              } />

              <Route path="/notes" element={
                <ProtectedRoute>
                  <Layout>
                    <Notes />
                  </Layout>
                </ProtectedRoute>
              } />

              <Route path="/tasks" element={
                <ProtectedRoute>
                  <Layout>
                    <Tasks />
                  </Layout>
                </ProtectedRoute>
              } />

              <Route path="/habits" element={
                <ProtectedRoute>
                  <Layout>
                    <Habits />
                  </Layout>
                </ProtectedRoute>
              } />

              <Route path="/health" element={
                <ProtectedRoute>
                  <Layout>
                    <Health />
                  </Layout>
                </ProtectedRoute>
              } />

              <Route path="/knowledge" element={
                <ProtectedRoute>
                  <Layout>
                    <Knowledge />
                  </Layout>
                </ProtectedRoute>
              } />

              <Route path="/finance" element={
                <ProtectedRoute>
                  <Layout>
                    <Finance />
                  </Layout>
                </ProtectedRoute>
              } />

              <Route path="/islamic" element={
                <ProtectedRoute>
                  <Layout>
                    <Islamic />
                  </Layout>
                </ProtectedRoute>
              } />

              <Route path="/settings" element={
                <ProtectedRoute>
                  <Layout>
                    <Settings />
                  </Layout>
                </ProtectedRoute>
              } />
            </Routes>
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
