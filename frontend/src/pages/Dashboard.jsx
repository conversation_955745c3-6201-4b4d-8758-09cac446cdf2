import React from 'react';
import { motion } from 'framer-motion';
import {
  CheckSquare,
  Target,
  FileText,
  Heart,
  DollarSign,
  Moon,
  Plus,
  TrendingUp,
  Clock,
  BookOpen,
  Star,
} from 'lucide-react';

const Dashboard = () => {
  const stats = [
    {
      name: 'Prayers Completed',
      value: '4/5',
      change: 'Maghrib Next',
      changeType: 'positive',
      icon: Moon,
      color: 'bg-primary-600',
      arabic: 'الصلوات',
    },
    {
      name: 'Quran Reading',
      value: '2 Pages',
      change: 'Surah Al-Baqarah',
      changeType: 'positive',
      icon: BookOpen,
      color: 'bg-accent-600',
      arabic: 'القرآن',
    },
    {
      name: 'Dhikr Count',
      value: '233',
      change: '<PERSON>han<PERSON>llah',
      changeType: 'positive',
      icon: Star,
      color: 'bg-primary-700',
      arabic: 'الذكر',
    },
    {
      name: 'Good Deeds',
      value: '7',
      change: '+3 Today',
      changeType: 'positive',
      icon: Heart,
      color: 'bg-accent-700',
      arabic: 'الأعمال الصالحة',
    },
  ];

  const quickActions = [
    { name: 'Add Task', icon: CheckSquare, href: '/tasks' },
    { name: 'Log Habit', icon: Target, href: '/habits' },
    { name: 'New Note', icon: FileText, href: '/notes' },
    { name: 'Track Health', icon: Heart, href: '/health' },
    { name: 'Add Expense', icon: DollarSign, href: '/finance' },
    { name: 'Prayer Times', icon: Moon, href: '/islamic' },
  ];

  return (
    <div className="space-y-6">
      {/* Islamic Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <div className="arabic-text text-primary-700 dark:text-primary-400 mb-2">
          بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيم
        </div>
        <h1 className="text-3xl font-heading font-bold text-primary-800 dark:text-primary-300 mb-2">
          السلام عليكم ورحمة الله وبركاته
        </h1>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-1">
          Assalamu Alaikum wa Rahmatullahi wa Barakatuh
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          May Allah bless your day with productivity and spiritual growth
        </p>
      </motion.div>

      {/* Islamic Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="islamic-card p-6 hover:scale-105 transition-transform duration-300"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`${stat.color} p-3 rounded-xl shadow-lg`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <div className="text-xs text-accent-600 dark:text-accent-400 font-arabic">
                  {stat.arabic}
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 font-heading">
                {stat.name}
              </h3>
              <div className="flex items-baseline justify-between">
                <div className="text-2xl font-bold text-primary-800 dark:text-primary-300 font-heading">
                  {stat.value}
                </div>
                <div className="text-xs text-accent-600 dark:text-accent-400 font-medium">
                  {stat.change}
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Prayer Times & Islamic Widgets */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Prayer Times Widget */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="lg:col-span-2 prayer-widget islamic-card"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-heading font-semibold text-primary-800 dark:text-primary-300">
              Prayer Times Today
            </h3>
            <div className="text-sm text-accent-600 dark:text-accent-400 font-arabic">
              أوقات الصلاة
            </div>
          </div>
          <div className="grid grid-cols-5 gap-3">
            {[
              { name: 'Fajr', time: '5:24 AM', arabic: 'الفجر', completed: true },
              { name: 'Dhuhr', time: '12:45 PM', arabic: 'الظهر', completed: true },
              { name: 'Asr', time: '4:15 PM', arabic: 'العصر', completed: true },
              { name: 'Maghrib', time: '6:42 PM', arabic: 'المغرب', completed: false, current: true },
              { name: 'Isha', time: '8:15 PM', arabic: 'العشاء', completed: false },
            ].map((prayer, index) => (
              <div
                key={prayer.name}
                className={`text-center p-3 rounded-xl transition-all duration-300 ${
                  prayer.current
                    ? 'prayer-active text-white'
                    : prayer.completed
                    ? 'bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200'
                    : 'bg-secondary-100 dark:bg-dark-800 text-gray-600 dark:text-gray-400'
                }`}
              >
                <div className="text-xs font-arabic mb-1">{prayer.arabic}</div>
                <div className="font-medium text-sm">{prayer.name}</div>
                <div className="text-xs mt-1">{prayer.time}</div>
                {prayer.completed && (
                  <div className="text-xs mt-1 text-primary-600">✓</div>
                )}
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Next prayer: <span className="font-medium text-accent-600">Maghrib in 2h 15m</span>
            </p>
          </div>
        </motion.div>

        {/* Tasbih Counter */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="islamic-card p-6 text-center"
        >
          <h3 className="text-lg font-heading font-semibold text-primary-800 dark:text-primary-300 mb-2">
            Digital Tasbih
          </h3>
          <div className="text-sm text-accent-600 dark:text-accent-400 font-arabic mb-4">
            المسبحة الرقمية
          </div>
          <div className="flex justify-center mb-4">
            <div className="tasbih-counter">
              233
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm font-arabic text-primary-700 dark:text-primary-400">
              سُبْحَانَ اللَّهِ
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              SubhanAllah
            </div>
            <button className="btn-accent text-xs px-4 py-2">
              Reset Counter
            </button>
          </div>
        </motion.div>
      </div>

      {/* Daily Verse & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Daily Quran Verse */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="islamic-card p-6"
        >
          <h3 className="text-lg font-heading font-semibold text-primary-800 dark:text-primary-300 mb-4">
            Verse of the Day
          </h3>
          <div className="quran-verse">
            <div className="font-arabic text-xl mb-3 text-primary-700 dark:text-primary-400">
              وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              "And whoever fears Allah - He will make for him a way out"
            </div>
            <div className="text-xs text-accent-600 dark:text-accent-400">
              Surah At-Talaq (65:2)
            </div>
          </div>
        </motion.div>

        {/* Quick Islamic Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="islamic-card p-6"
        >
          <h3 className="text-lg font-heading font-semibold text-primary-800 dark:text-primary-300 mb-4">
            Islamic Actions
          </h3>
          <div className="grid grid-cols-2 gap-3">
            {[
              { name: 'Read Quran', icon: BookOpen, arabic: 'قراءة القرآن' },
              { name: 'Prayer Times', icon: Clock, arabic: 'أوقات الصلاة' },
              { name: 'Dhikr', icon: Star, arabic: 'الذكر' },
              { name: 'Dua', icon: Heart, arabic: 'الدعاء' },
            ].map((action, index) => (
              <motion.button
                key={action.name}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.5 + index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex flex-col items-center p-4 rounded-xl bg-secondary-100 dark:bg-dark-800 hover:bg-primary-100 dark:hover:bg-primary-900 transition-all duration-300"
              >
                <action.icon className="h-6 w-6 text-primary-600 dark:text-primary-400 mb-2" />
                <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {action.name}
                </span>
                <span className="text-xs text-accent-600 dark:text-accent-400 font-arabic mt-1">
                  {action.arabic}
                </span>
              </motion.button>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
            Recent Activity
          </h3>
          <div className="mt-5">
            <div className="flow-root">
              <ul className="-mb-8">
                {[
                  {
                    id: 1,
                    content: 'Completed morning workout',
                    time: '2 hours ago',
                    icon: Heart,
                    iconBackground: 'bg-red-500',
                  },
                  {
                    id: 2,
                    content: 'Added new note about project ideas',
                    time: '4 hours ago',
                    icon: FileText,
                    iconBackground: 'bg-purple-500',
                  },
                  {
                    id: 3,
                    content: 'Completed daily reading habit',
                    time: '6 hours ago',
                    icon: Target,
                    iconBackground: 'bg-green-500',
                  },
                ].map((item, itemIdx, items) => (
                  <li key={item.id}>
                    <div className="relative pb-8">
                      {itemIdx !== items.length - 1 ? (
                        <span
                          className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-700"
                          aria-hidden="true"
                        />
                      ) : null}
                      <div className="relative flex space-x-3">
                        <div>
                          <span
                            className={`${item.iconBackground} h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-800`}
                          >
                            <item.icon className="h-4 w-4 text-white" />
                          </span>
                        </div>
                        <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                          <div>
                            <p className="text-sm text-gray-900 dark:text-white">
                              {item.content}
                            </p>
                          </div>
                          <div className="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                            {item.time}
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
