{"name": "lifeos-frontend", "private": true, "version": "1.0.0", "description": "LifeOS Frontend - Integrated productivity, health, finance, and Islamic features platform", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@supabase/supabase-js": "^2.56.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.11.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.8.2", "react-syntax-highlighter": "^15.6.6", "recharts": "^3.1.2", "zod": "^4.1.5"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "vite": "^7.1.2"}}