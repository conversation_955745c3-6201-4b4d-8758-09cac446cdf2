# LifeOS

A modern full-stack web application that integrates productivity, health, finance, Islamic features, and AI assistance into one seamless system.

## 🚀 Features

- **Dashboard**: Overview of your day with customizable widgets
- **Notes**: Rich-text editor with Markdown support and AI summarization
- **Tasks**: To-do list with priorities, due dates, and calendar integration
- **Habits**: Daily/weekly habit tracking with streaks and progress charts
- **Health**: Gym, running, and sleep tracking with Google Fit/Apple Health sync
- **Knowledge Base**: Personal wiki with AI-powered Q&A
- **Finance**: Expense tracking, budgets, and banking API integration
- **Islamic Module**: Prayer times, Quran reading, Hadith reminders, and fasting tracker
- **Settings**: Comprehensive customization and preferences

## 🛠️ Tech Stack

### Frontend
- **React** with Vite for fast development
- **TailwindCSS** for styling
- **Framer Motion** for animations
- **React Router** for navigation
- **Axios** for API communication
- **React Hook Form** for form handling
- **Lucide React** for icons

### Backend
- **Node.js** with Express.js
- **Supabase** for database and authentication
- **Google AI (Gemini)** for AI features
- **JWT** for authentication
- **bcryptjs** for password hashing

### Database
- **PostgreSQL** via Supabase
- Row Level Security (RLS) enabled
- Real-time subscriptions

## 📁 Project Structure

```
LifeOS/
├── frontend/                 # React frontend
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── contexts/       # React contexts
│   │   ├── services/       # API services
│   │   ├── hooks/          # Custom hooks
│   │   └── utils/          # Utility functions
│   ├── public/             # Static assets
│   └── package.json
├── backend/                 # Node.js backend
│   ├── src/
│   │   ├── routes/         # API routes
│   │   ├── controllers/    # Route controllers
│   │   ├── middleware/     # Express middleware
│   │   ├── services/       # Business logic
│   │   ├── config/         # Configuration files
│   │   └── utils/          # Utility functions
│   └── package.json
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd LifeOS
   ```

2. **Install backend dependencies**
   ```bash
   cd backend
   npm install
   ```

3. **Install frontend dependencies**
   ```bash
   cd ../frontend
   npm install
   ```

4. **Environment Setup**
   
   **Backend** (`backend/.env`):
   ```env
   PORT=5000
   NODE_ENV=development
   FRONTEND_URL=http://localhost:5173
   
   # Supabase
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   
   # JWT
   JWT_SECRET=your_super_secret_jwt_key
   JWT_EXPIRES_IN=7d
   
   # Google AI
   GOOGLE_AI_API_KEY=your_google_ai_api_key
   ```
   
   **Frontend** (`frontend/.env`):
   ```env
   VITE_API_URL=http://localhost:5000/api
   ```

5. **Start the development servers**
   
   **Backend** (Terminal 1):
   ```bash
   cd backend
   npm run dev
   ```
   
   **Frontend** (Terminal 2):
   ```bash
   cd frontend
   npm run dev
   ```

6. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:5000
   - Health Check: http://localhost:5000/health

## 🗄️ Database Schema

The application uses the following main tables:
- `users` - User accounts and profiles
- `notes` - User notes with rich content
- `tasks` - To-do items and task management
- `habits` - Habit definitions and tracking
- `habit_logs` - Daily habit completion logs
- `health_logs` - Health and fitness data
- `knowledge_base` - Personal wiki entries
- `finance_transactions` - Financial transactions
- `budgets` - Budget definitions
- `islamic_preferences` - Islamic feature settings
- `prayer_logs` - Prayer completion tracking
- `user_settings` - User preferences and settings

## 🔧 Development

### Available Scripts

**Backend:**
- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server

**Frontend:**
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

### Code Style
- ESLint for code linting
- Prettier for code formatting
- Conventional commits for commit messages

## 🚀 Deployment

### Backend Deployment
1. Set up environment variables
2. Deploy to your preferred platform (Vercel, Railway, etc.)
3. Ensure Supabase connection is configured

### Frontend Deployment
1. Build the application: `npm run build`
2. Deploy the `dist` folder to your hosting platform
3. Configure environment variables for production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- React team for the amazing framework
- Supabase for the backend infrastructure
- TailwindCSS for the utility-first CSS framework
- All the open-source contributors who made this possible
